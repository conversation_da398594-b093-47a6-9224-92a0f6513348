name: Build and Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - development


jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
   



    - name: Download environment file from production server
      if: ${{ github.event.inputs.environment == 'production' }}
      run: |
        sudo apt-get update && sudo apt-get install -y sshpass
        sshpass -p "${{ secrets.PROD_PASSWORD }}" scp -o StrictHostKeyChecking=no \
          "${{ secrets.PROD_USERNAME }}@${{ secrets.PROD_HOST }}:${{ secrets.PROD_DEPLOY_PATH }}/${{ secrets.ENV_FILE_NAME }}" \
          "./${{ secrets.ENV_FILE_NAME }}"

    - name: Download environment file from development server
      if: ${{ github.event.inputs.environment == 'development' }}
      run: |
        sudo apt-get update && sudo apt-get install -y sshpass
        sshpass -p "${{ secrets.DEV_PASSWORD }}" scp -o StrictHostKeyChecking=no \
          "${{ secrets.DEV_USERNAME }}@${{ secrets.DEV_HOST }}:${{ secrets.DEV_DEPLOY_PATH }}/${{ secrets.ENV_FILE_NAME }}" \
          "./${{ secrets.ENV_FILE_NAME }}"

    - name: Verify environment file was copied
      run: |
        ENV_FILE="${{ secrets.ENV_FILE_NAME }}"
        if [ -f "$ENV_FILE" ]; then
          echo "✅ Environment file $ENV_FILE found"
          echo "File size: $(stat -c%s "$ENV_FILE") bytes"
          echo "First few lines :"
          head -5 "$ENV_FILE" 
        else
          echo "❌ Error: Environment file $ENV_FILE not found"
          echo "Current directory contents:"
          ls -la
          exit 1
        fi
    

    - name: Install dependencies
      run: npm ci --legacy-peer-deps

    - name: Clear Next.js cache
      run: rm -rf .next

    - name: Load environment variables
      run: |
        ENV_FILE="${{ secrets.ENV_FILE_NAME }}"
        if [ -f "$ENV_FILE" ]; then
          echo "Loading environment variables from $ENV_FILE"
          # Create a temporary script to export all variables
          cat "$ENV_FILE" | grep -v '^#' | grep -v '^$' > /tmp/env_vars.sh
          echo "Environment variables loaded:"
          echo "NODE_ENV=$(grep '^NODE_ENV=' /tmp/env_vars.sh | cut -d'=' -f2 || echo 'not set')"
          echo "NEXTAUTH_URL=$(grep '^NEXTAUTH_URL=' /tmp/env_vars.sh | cut -d'=' -f2 || echo 'not set')"
        else
          echo "No environment file found"
        fi

    - name: Build project
      run: |
        ENV_FILE="${{ secrets.ENV_FILE_NAME }}"
        if [ -f "$ENV_FILE" ]; then
          # Source the environment file and export all variables
          set -a
          source "$ENV_FILE"
          set +a
        fi

        # Ensure critical environment variables have fallback values
        # Always use 'production' for NODE_ENV during build, regardless of deployment environment
        export NODE_ENV="production"
        export NEXTAUTH_URL="${NEXTAUTH_URL:-http://localhost:5000}"

        # Fix translation file paths for GitHub Actions environment
        # These paths are likely Windows paths that don't exist in Linux
        export EN_PATH="${EN_PATH:-./src/locales/en.json}"
        export FR_PATH="${FR_PATH:-./src/locales/fr.json}"

        echo "Building with NODE_ENV=$NODE_ENV"
        NODE_OPTIONS="--max-old-space-size=4096" npm run build
      env:
        NEXT_TELEMETRY_DISABLED: 1
    
    - name: Create deployment artifact
      run: |
        tar --exclude=node_modules/.cache \
          -czf deployment.tar.gz \
          .next \
          public \
          package.json \
          package-lock.json \
          next.config.js \
          ecosystem.config.js \
          scripts \
          node_modules
    
    - name: Upload build artifact
      uses: actions/upload-artifact@v4
      with:
        name: deployment-artifact
        path: deployment.tar.gz
        retention-days: 1

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.environment == 'production' }}
    
    steps:
    - name: Download build artifact
      uses: actions/download-artifact@v4
      with:
        name: deployment-artifact
    
    - name: Copy deployment artifact to server
      uses: appleboy/scp-action@v1
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USERNAME }}
        password: ${{ secrets.PROD_PASSWORD }}
        port: 22
        source: "deployment.tar.gz"
        target: "/tmp/"

    - name: Deploy to Production Server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USERNAME }}
        password: ${{ secrets.PROD_PASSWORD }}
        port: 22
        script: |
          cd ${{ secrets.PROD_DEPLOY_PATH }}

          # Create backup of current deployment
          sudo cp -r .next .next.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

          # Extract the new build
          sudo tar -xzf /tmp/deployment.tar.gz -C ${{ secrets.PROD_DEPLOY_PATH }} --overwrite

          # Set proper permissions
          sudo chown -R www-data:www-data ${{ secrets.PROD_DEPLOY_PATH }}
          sudo chmod 755 ./scripts/thank_clients.sh 2>/dev/null || true

          # Clean up
          rm -f /tmp/deployment.tar.gz

          echo "Deployment completed for production"

          # Create logs directory if it doesn't exist
          sudo mkdir -p logs

          # Set environment variables for PM2 ecosystem
          export PROD_APP_NAME="${{ secrets.PROD_APP_NAME }}"
          export DEV_APP_NAME="${{ secrets.DEV_APP_NAME }}"
          export PROD_DEPLOY_PATH="${{ secrets.PROD_DEPLOY_PATH }}"
          export DEV_DEPLOY_PATH="${{ secrets.DEV_DEPLOY_PATH }}"
          export ENV_FILE_NAME="${{ secrets.ENV_FILE_NAME }}"
          export PROD_PORT="${{ secrets.PROD_PORT }}"
          export DEV_PORT="${{ secrets.DEV_PORT }}"
          export PROD_MEMORY_LIMIT="${{ secrets.PROD_MEMORY_LIMIT }}"
          export DEV_MEMORY_LIMIT="${{ secrets.DEV_MEMORY_LIMIT }}"
          export PROD_NODE_MEMORY="${{ secrets.PROD_NODE_MEMORY }}"
          export DEV_NODE_MEMORY="${{ secrets.DEV_NODE_MEMORY }}"

          # Start or restart PM2 process using ecosystem file
          if pm2 describe "${{ secrets.PROD_APP_NAME }}" > /dev/null 2>&1; then
            pm2 restart "${{ secrets.PROD_APP_NAME }}" --update-env
          else
            pm2 start ecosystem.config.js --only "${{ secrets.PROD_APP_NAME }}"
          fi

  deploy-development:
    needs: build
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.environment == 'development' }}
    
    steps:
    - name: Download build artifact
      uses: actions/download-artifact@v4
      with:
        name: deployment-artifact
    
    - name: Copy deployment artifact to server
      uses: appleboy/scp-action@v1
      with:
        host: ${{ secrets.DEV_HOST }}
        username: ${{ secrets.DEV_USERNAME }}
        password: ${{ secrets.DEV_PASSWORD }}
        port: 22
        source: "deployment.tar.gz"
        target: "/tmp/"

    - name: Deploy to Development Server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.DEV_HOST }}
        username: ${{ secrets.DEV_USERNAME }}
        password: ${{ secrets.DEV_PASSWORD }}
        port: 22
        script: |
          cd ${{ secrets.DEV_DEPLOY_PATH }}

          # Create backup of current deployment
          sudo cp -r .next .next.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

          # Extract the new build
          sudo tar -xzf /tmp/deployment.tar.gz -C ${{ secrets.DEV_DEPLOY_PATH }} --overwrite

          # Set proper permissions
          sudo chown -R www-data:www-data ${{ secrets.DEV_DEPLOY_PATH }}
          sudo chmod 755 ./scripts/thank_clients.sh 2>/dev/null || true

          # Clean up
          rm -f /tmp/deployment.tar.gz

          echo "Deployment completed for development"

          # Create logs directory if it doesn't exist
          sudo mkdir -p logs

          # Set environment variables for PM2 ecosystem
          export PROD_APP_NAME="${{ secrets.PROD_APP_NAME }}"
          export DEV_APP_NAME="${{ secrets.DEV_APP_NAME }}"
          export PROD_DEPLOY_PATH="${{ secrets.PROD_DEPLOY_PATH }}"
          export DEV_DEPLOY_PATH="${{ secrets.DEV_DEPLOY_PATH }}"
          export ENV_FILE_NAME="${{ secrets.ENV_FILE_NAME }}"
          export PROD_PORT="${{ secrets.PROD_PORT }}"
          export DEV_PORT="${{ secrets.DEV_PORT }}"
          export PROD_MEMORY_LIMIT="${{ secrets.PROD_MEMORY_LIMIT }}"
          export DEV_MEMORY_LIMIT="${{ secrets.DEV_MEMORY_LIMIT }}"
          export PROD_NODE_MEMORY="${{ secrets.PROD_NODE_MEMORY }}"
          export DEV_NODE_MEMORY="${{ secrets.DEV_NODE_MEMORY }}"

          # Ensure PM2 directories exist with proper permissions
          mkdir -p ~/.pm2/logs
          mkdir -p ${{ secrets.DEV_DEPLOY_PATH }}/logs
          chmod 755 ~/.pm2/logs
          chmod 755 ${{ secrets.DEV_DEPLOY_PATH }}/logs

          # Start or restart PM2 process using ecosystem file
          if pm2 describe "${{ secrets.DEV_APP_NAME }}" > /dev/null 2>&1; then
            pm2 restart "${{ secrets.DEV_APP_NAME }}" --update-env
          else
            pm2 start ecosystem.config.js --only "${{ secrets.DEV_APP_NAME }}"
          fi
